"use client";
import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  TableCell,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
} from "@nextui-org/react";
import { But<PERSON> } from "@heroui/button";

import CreateContactForm from "./create-contact-form";

interface ContactsModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedProject: number | null;
}

const dummyContacts = [
  {
    type: "Empresa",
    name: "<PERSON>",
    email: "<EMAIL>",
    position: "Director",
  },
  {
    type: "Agregador",
    name: "<PERSON>",
    email: "<EMAIL>",
    position: "<PERSON><PERSON><PERSON>",
  },
  {
    type: "Add-on",
    name: "<PERSON>",
    email: "<EMAIL>",
    position: "Especialista",
  },
  {
    type: "O<PERSON>",
    name: "<PERSON>",
    email: "<EMAIL>",
    position: "<PERSON><PERSON><PERSON>",
  },
  {
    type: "Impuestos",
    name: "<PERSON>",
    email: "<EMAIL>",
    position: "<PERSON>tador",
  },
  {
    type: "SS",
    name: "<PERSON> <PERSON>",
    email: "<EMAIL>",
    position: "Analista",
  },
  {
    type: "GL File",
    name: "Pedro Sánchez",
    email: "<EMAIL>",
    position: "Administrador",
  },
];

export const ContactsModal = ({
  isOpen,
  onClose,
  selectedProject,
}: ContactsModalProps) => {
  const [contacts, setContacts] = useState(dummyContacts);
  const [createModalOpen, setCreateModalOpen] = useState(false);

  const handleAddContact = (newContact: any) => {
    setContacts((prev) => [...prev, newContact]);
  };

  return (
    <>
      <Modal isDismissable={false} isOpen={isOpen} size="3xl" onClose={onClose}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                <div className="flex justify-between items-center w-full">
                  <h2>Contactos</h2>
                </div>
              </ModalHeader>
              <ModalBody>
                <Table removeWrapper selectionMode="single">
                  <TableHeader>
                    <TableColumn>Tipo de contacto</TableColumn>
                    <TableColumn>Nombre</TableColumn>
                    <TableColumn>Correo</TableColumn>
                    <TableColumn>Cargo</TableColumn>
                  </TableHeader>
                  <TableBody>
                    {contacts.map((contact, index) => (
                      <TableRow key={index}>
                        <TableCell>{contact.type}</TableCell>
                        <TableCell>{contact.name}</TableCell>
                        <TableCell className="underline">
                          <a href={`mailto:${contact.email}`}>
                            {contact.email}
                          </a>
                        </TableCell>
                        <TableCell>{contact.position}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ModalBody>
              <ModalFooter className="flex justify-between">
                <Button
                  color="primary"
                  onPress={() => setCreateModalOpen(true)}
                >
                  Crear Contacto
                </Button>
                <Button color="primary" onPress={onClose}>
                  Cerrar
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>

      {/* Create Contact Modal */}
      <Modal
        isDismissable={false}
        isOpen={createModalOpen}
        onClose={() => setCreateModalOpen(false)}
      >
        <ModalContent>
          {() => (
            <CreateContactForm
              projectId={selectedProject}
              onAddContact={handleAddContact}
              onClose={() => setCreateModalOpen(false)}
            />
          )}
        </ModalContent>
      </Modal>
    </>
  );
};
