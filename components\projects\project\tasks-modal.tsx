import {
  Modal,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  ModalBody,
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
} from "@heroui/modal";

export interface TasksModalProps {
  isOpen: boolean;
  onClose: () => void;
  tasks: any[];
}

export const TasksModal = ({ isOpen, onClose, tasks }: TasksModalProps) => {
  return (
    <>
      <Modal isOpen={isOpen} size="2xl" onClose={onClose}>
        <ModalContent>
          {(onClose) => {
            <>
              <ModalHeader>Tasks</ModalHeader>
              <ModalBody>
                <Table>
                  <TableHeader>
                    <TableColumn>Task Name</TableColumn>
                    <TableColumn>Description</TableColumn>
                    <TableColumn>Status</TableColumn>
                  </TableHeader>
                  <TableBody>
                    {tasks.map((task, index) => (
                      <TableRow key={index}>
                        <TableCell>{task.name}</TableCell>
                        <TableCell>{task.description}</TableCell>
                        <TableCell>{task.status}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </ModalBody>
            </>;
          }}
        </ModalContent>
      </Modal>
    </>
  );
};
